# 更新流程改进说明

## 修改概述

根据用户需求，更新流程已经改为：

### 自动更新开启时
- **后台自动下载**：发现新版本时不立即弹窗，而是在后台静默下载
- **控制台输出进度**：下载过程中在控制台输出进度信息，不显示UI进度条
- **下载完成提示**：下载完成后弹出对话框让用户选择是否立即安装

### 自动更新关闭时
- **显示新版本标签**：在主界面右上角显示新版本可用的标签
- **用户主动下载**：用户点击标签后开始下载并显示进度UI
- **下载完成提示**：下载完成后弹出安装提示

## 修改的文件

### 1. client/src/main/main.js
- **update-available事件**：移除自动更新开启时的立即弹窗
- **update-downloaded事件**：添加自动更新开启时的安装提示弹窗
- **settings-changed监听器**：处理设置更改通知，动态调整autoUpdater配置

### 2. client/src/renderer/pages/index.html
- **新版本标签样式**：添加CSS样式，支持普通状态和下载状态
- **新版本标签元素**：在页面右上角添加标签元素

### 3. client/src/renderer/pages/renderer.js
- **更新状态管理**：添加currentUpdateInfo、autoUpdateEnabled、isDownloading状态
- **事件监听**：监听update-available、update-download-progress、update-downloaded、update-error事件
- **新版本标签控制**：显示/隐藏/更新标签的函数
- **点击处理**：处理用户点击新版本标签的逻辑
- **设置变化响应**：监听settings-changed事件，动态调整界面

### 4. client/src/renderer/utils/update-card.js
- **条件显示**：确保只在自动更新关闭时显示进度UI
- **设置刷新**：已有refreshSettings方法处理设置变化

### 5. client/src/renderer/pages/settings.js
- **设置变化通知**：当自动更新设置改变时发送通知到主进程

## 用户体验改进

### 自动更新用户（autoUpdate: true）
1. 发现新版本 → 后台静默下载（控制台显示进度）
2. 下载完成 → 弹出安装确认对话框
3. 用户选择立即安装或稍后安装

### 手动更新用户（autoUpdate: false）
1. 发现新版本 → 主界面显示"🚀 新版本 vX.X.X 可用"标签
2. 用户点击标签 → 开始下载，标签变为"📥 下载中 X%"
3. 下载完成 → 隐藏标签，显示update-card的安装提示

## 技术实现要点

1. **状态同步**：主进程和渲染进程之间的更新状态同步
2. **设置响应**：动态响应自动更新设置的变化
3. **UI条件显示**：根据自动更新设置决定显示哪种UI
4. **进度区分**：自动更新时只在控制台输出，手动更新时显示完整UI
5. **安装提示统一**：两种模式下载完成后都有安装提示

## 测试建议

1. **自动更新开启测试**：
   - 设置autoUpdate为true
   - 触发更新检查
   - 验证后台下载和安装提示

2. **自动更新关闭测试**：
   - 设置autoUpdate为false
   - 触发更新检查
   - 验证新版本标签显示和点击下载

3. **设置切换测试**：
   - 在有可用更新时切换自动更新设置
   - 验证界面响应正确

4. **状态恢复测试**：
   - 在下载过程中重启应用
   - 验证状态正确恢复
